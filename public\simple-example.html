<template>
  <div class="ai-chat-window" v-if="isOpen">
    <div class="ai-chat-header">
      <div class="ai-chat-title">客服助手</div>
      <button class="ai-chat-close" @click="$emit('close')">&times;</button>
    </div>
    <div class="ai-chat-messages" ref="messagesContainer">
      <div 
        v-for="(message, index) in messages" 
        :key="index" 
        class="message" 
        :class="{ user: message.sender === 'user' }"
      >
        {{ message.text }}
      </div>
    </div>
    <div class="ai-chat-input">
      <input 
        type="text" 
        v-model="inputText" 
        @keyup.enter="sendMessage()" 
        placeholder="请输入您的问题..."
      />
      <button @click="sendMessage()">发送</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatWindow',
  props: {
    isOpen: Boolean,
    apiUrl: String,
    apiKey: String
  },
  data() {
    return {
      messages: [
        { sender: 'bot', text: '您好！我是 AI 客服助手，请问有什么可以帮您？' }
      ],
      inputText: '',
      loading: false
    };
  },
  watch: {
    messages() {
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    isOpen(val) {
      if (val) {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    }
  },
  methods: {
    scrollToBottom() {
      const container = this.$refs.messagesContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },
    async sendMessage(text) {
      const messageText = text || this.inputText;
      if (!messageText || this.loading) return;
      
      // 添加用户消息
      this.messages.push({ sender: 'user', text: messageText });
      this.inputText = '';
      this.loading = true;
      
      try {
        // 模拟 API 调用
        // 实际项目中应替换为真实的 API 调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 假设的 API 回复
        this.messages.push({ sender: 'bot', text: `您的问题"${messageText}"已收到，我们正在处理。` });
      } catch (error) {
        console.error('发送消息失败：', error);
        this.messages.push({ sender: 'bot', text: '抱歉，消息发送失败，请稍后再试。' });
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>

<style scoped>
.ai-chat-window {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 350px;
  height: 450px;
  border-radius: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  background-color: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 9998;
}

.ai-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #4a86e8;
  color: white;
}

.ai-chat-title {
  font-weight: bold;
}

.ai-chat-close {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
}

.ai-chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.message {
  padding: 10px 15px;
  border-radius: 18px;
  max-width: 80%;
  word-break: break-word;
  background-color: #e1e9f8;
}

.message.user {
  align-self: flex-end;
  background-color: #4a86e8;
  color: white;
}

.ai-chat-input {
  display: flex;
  padding: 10px;
  border-top: 1px solid #eee;
}

.ai-chat-input input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 20px;
  outline: none;
  margin-right: 8px;
}

.ai-chat-input button {
  background-color: #4a86e8;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 15px;
  cursor: pointer;
}
</style> 