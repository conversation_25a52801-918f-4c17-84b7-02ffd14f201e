<template>
  <div id="app">
    <div class="dev-instruction-container">
      <h1 class="dev-title">AI 客服组件开发环境说明</h1>
      <div class="dev-content">
        <section>
          <h2>项目简介</h2>
          <p>本页面仅在开发环境下可见，用于调试和开发 AI 客服组件。右下角为实际嵌入效果。</p>
        </section>
        <section>
          <h2>配置项说明</h2>
          <table class="config-table">
            <tr>
              <th>配置项</th>
              <th>类型</th>
              <th>默认值</th>
              <th>说明</th>
            </tr>
            <tr>
              <td><code>position</code></td>
              <td>string</td>
              <td>'bottom-right'</td>
              <td>聊天按钮的基础位置，可选值：'bottom-right'、'bottom-left'、'top-right'、'top-left'</td>
            </tr>
            <tr>
              <td><code>offsetX</code></td>
              <td>number</td>
              <td>20</td>
              <td>水平方向偏移量（像素），表示按钮距离屏幕边缘的距离</td>
            </tr>
            <tr>
              <td><code>offsetY</code></td>
              <td>number</td>
              <td>20</td>
              <td>垂直方向偏移量（像素），表示按钮距离屏幕边缘的距离</td>
            </tr>
            <tr>
              <td><code>businessId</code></td>
              <td>string</td>
              <td>''</td>
              <td>业务 ID，用于通过 /knowledge/ids 接口获取知识库文档 ID 列表</td>
            </tr>
            <tr>
              <td><code>openMode</code></td>
              <td>string</td>
              <td>'iframe'</td>
              <td>聊天界面的打开方式，可选值：'iframe'（弹窗模式）、'newTab'（新标签页模式）</td>
            </tr>
            <tr>
              <td><code>onOpen</code></td>
              <td>function</td>
              <td>null</td>
              <td>聊天窗口打开时的回调函数</td>
            </tr>
            <tr>
              <td><code>onClose</code></td>
              <td>function</td>
              <td>null</td>
              <td>聊天窗口关闭时的回调函数</td>
            </tr>
          </table>
          <div class="config-note">
            <p><b>position 与 offsetX/offsetY 配合使用：</b></p>
            <ul>
              <li><b>bottom-right</b>: offsetX 控制距右侧距离，offsetY 控制距底部距离</li>
              <li><b>bottom-left</b>: offsetX 控制距左侧距离，offsetY 控制距底部距离</li>
              <li><b>top-right</b>: offsetX 控制距右侧距离，offsetY 控制距顶部距离</li>
              <li><b>top-left</b>: offsetX 控制距左侧距离，offsetY 控制距顶部距离</li>
            </ul>
          </div>
        </section>
        <section>
          <h2>快速开始</h2>
          <ol>
            <li>修改 <code>src/App.vue</code> 中的 <code>devConfig</code> 配置来测试不同位置效果</li>
            <li>保存文件后，页面会自动热更新</li>
          </ol>
        </section>
        <div class="dev-hint">
          <b>提示：</b>右下角按钮为实际组件效果，点击可体验对话
        </div>
      </div>
    </div>
    <ChatApp :config="devConfig" />
  </div>
</template>

<script>
import ChatApp from './components/ChatApp.vue';

export default {
  name: 'App',
  components: {
    ChatApp
  },
  data() {
    return {
      devConfig: {
        url: "https://dev.shukeyun.com/data/ai-platform-frontend/#/intelligent/preview/7355805260352327680",
        position: 'bottom-right',
        offsetX: 20,
        offsetY: 20,
        businessId: 177711
      }
    };
  }
}
</script>

<style>
/* 添加全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  overflow-x: hidden;
}

#app {
  font-family: Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  min-height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dev-instruction-container {
  max-width: 700px;
  padding: 32px 36px;
  background: #f9f9fb;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
  text-align: left;
}

.dev-title {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  color: #2b6cb0;
}

.dev-content section {
  margin-bottom: 1.5rem;
}

.dev-content h2 {
  font-size: 1.2rem;
  margin: 1rem 0 0.5rem 0;
  color: #4a5568;
}

.dev-content p, .dev-content li {
  color: #555;
  font-size: 1rem;
}

.dev-content code {
  background: #edf2f7;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.95em;
}

.config-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  font-size: 0.9rem;
}

.config-table th, .config-table td {
  border: 1px solid #e2e8f0;
  padding: 8px 12px;
  text-align: left;
}

.config-table th {
  background-color: #edf2f7;
  color: #4a5568;
  font-weight: 600;
}

.config-note {
  background-color: #ebf8ff;
  border-left: 4px solid #4299e1;
  padding: 0.75rem 1rem;
  margin: 1rem 0;
  border-radius: 0 4px 4px 0;
}

.config-note p {
  margin-top: 0;
}

.config-note ul {
  margin-bottom: 0;
}

.dev-hint {
  margin-top: 2rem;
  padding: 1rem;
  background-color: #ebf8ff;
  border-radius: 8px;
  color: #3182ce;
  font-weight: bold;
  text-align: center;
}
</style> 