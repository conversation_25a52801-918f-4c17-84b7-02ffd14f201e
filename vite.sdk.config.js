import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { fileURLToPath, URL } from 'node:url';
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js';

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    cssInjectedByJsPlugin()
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  build: {
    lib: {
      entry: './src/sdk/index.js',
      name: 'AIChatSDK',
      fileName: 'ai-chat-sdk'
    },
    cssCodeSplit: false,
    assetsInlineLimit: 100000000,
    rollupOptions: {
      output: {
        manualChunks: undefined,
        entryFileNames: 'ai-chat-sdk.js',
        chunkFileNames: 'ai-chat-sdk-chunk.js',
        assetFileNames: (assetInfo) => {
          if (assetInfo.name) {
            return `ai-chat-sdk-asset-${assetInfo.name}`;
          }
          return 'ai-chat-sdk[extname]';
        }
      },
      // external: ['vue'],
    }
  },
  define: {
    'process.env': {},
    'process.env.NODE_ENV': JSON.stringify('production')
  }
}); 