<template>
  <div id="ai-chat-container">
    <chat-button @click="toggleChat" :position="config.position" :offsetX="config.offsetX || 20"
      :offsetY="config.offsetY || 20" />
    <chat-window v-if="(config.openMode || 'iframe') === 'iframe'" ref="chatWindow" :isOpen="isOpen" :config="config"
      @close="toggleChat(false)" />
  </div>
</template>

<script>
import ChatButton from './ChatButton.vue';
import ChatWindow from './ChatWindow.vue';

export default {
  name: 'ChatApp',
  components: {
    ChatButton,
    ChatWindow
  },
  props: {
    config: {
      type: Object,
      default: () => ({
        position: 'bottom-right',
        offsetX: 20,
        offsetY: 20,
        openMode: 'iframe' // 默认使用 iframe 模式
      })
    }
  },
  data() {
    return {
      isOpen: false
    };
  },
  methods: {
    toggleChat(show = !this.isOpen) {
      // 根据配置的 openMode 决定打开方式
      const openMode = this.config.openMode || 'iframe'; // 向后兼容，默认 iframe 模式

      if (show && openMode === 'newTab') {
        // newTab 模式：在新标签页/窗口中打开链接
        if (this.config.url) {
          window.open(this.config.url, '_blank');
          // 触发打开回调
          if (typeof this.config.onOpen === 'function') {
            this.config.onOpen();
          }
        } else {
          console.warn('AI Chat SDK: 在 newTab 模式下需要配置 url 参数');
        }
        return; // newTab 模式下不需要切换 iframe 窗口状态
      }

      // iframe 模式：使用原有的弹窗逻辑
      this.isOpen = show;

      // 触发回调（如果存在）
      if (this.isOpen && typeof this.config.onOpen === 'function') {
        this.config.onOpen();
      } else if (!this.isOpen && typeof this.config.onClose === 'function') {
        this.config.onClose();
      }
    },
    openChat() {
      this.toggleChat(true);
    },
    closeChat() {
      this.toggleChat(false);
    },

  }
}
</script>

<style scoped>
#ai-chat-container {
  font-family: Arial, sans-serif;
  line-height: 1.5;
  color: #333;
}
</style> 