<template>
  <div id="ai-chat-container">
    <chat-button 
      @click="toggleChat" 
      :position="config.position"
      :offsetX="config.offsetX || 20"
      :offsetY="config.offsetY || 20"
    />
    <chat-window 
      ref="chatWindow"
      :isOpen="isOpen"
      :config="config"
      @close="toggleChat(false)"
    />
  </div>
</template>

<script>
import ChatButton from './ChatButton.vue';
import ChatWindow from './ChatWindow.vue';

export default {
  name: 'ChatApp',
  components: {
    ChatButton,
    ChatWindow
  },
  props: {
    config: {
      type: Object,
      default: () => ({
        position: 'bottom-right',
        offsetX: 20,
        offsetY: 20
      })
    }
  },
  data() {
    return {
      isOpen: false
    };
  },
  methods: {
    toggleChat(show = !this.isOpen) {
      this.isOpen = show;
      
      // 触发回调（如果存在）
      if (this.isOpen && typeof this.config.onOpen === 'function') {
        this.config.onOpen();
      } else if (!this.isOpen && typeof this.config.onClose === 'function') {
        this.config.onClose();
      }
    },
    openChat() {
      this.toggleChat(true);
    },
    closeChat() {
      this.toggleChat(false);
    },

  }
}
</script>

<style scoped>
#ai-chat-container {
  font-family: Arial, sans-serif;
  line-height: 1.5;
  color: #333;
}
</style> 