import { createApp } from 'vue';
import ChatApp from '../components/ChatApp.vue';
import envConfig, { environment } from '../config/environment';

// 阻止 Vue 产生开发警告
// 根据当前环境决定是否抑制 Vue 警告
const suppressVueWarnings = () => {
  // 检查是否为生产环境
  if (environment === 'prod') {
    const originalConsoleWarn = console.warn;
    console.warn = (...args) => {
      if (typeof args[0] === 'string' && args[0].includes('[Vue warn]')) {
        return;
      }
      originalConsoleWarn.apply(console, args);
    };
  }
};

// 初始化时处理 Vue 警告
suppressVueWarnings();

class AIChatSDK {
  constructor(config = {}) {
		// 默认配置
		const defaultConfig = {
			position: "bottom-right",
			offsetX: 20,
			offsetY: 20,
			openMode: "iframe" // 默认使用iframe模式
		}

		// 合并环境配置、默认配置和用户配置
		this.config = {
			...envConfig,
			...defaultConfig,
			...config,
			environment // 确保环境信息也传递给组件
		}

		this.app = null
		this.mountElement = null
		this.init()
	}
  
  init() {
    // 创建 DOM 挂载点
    this.mountElement = document.createElement('div');
    const mountId = `ai-chat-sdk-${Date.now()}`;
    this.mountElement.id = mountId;
    document.body.appendChild(this.mountElement);
    
    // 创建 Vue 应用实例
    this.app = createApp(ChatApp, {
      config: this.config
    });
    
    // 挂载应用
    this.instance = this.app.mount(`#${mountId}`);
  }
  
  // 公共 API
  openChat() {
    if (this.instance) {
      this.instance.openChat();
    }
  }
  
  closeChat() {
    if (this.instance) {
      this.instance.closeChat();
    }
  }
  

  
  destroy() {
    if (this.app) {
      this.app.unmount();
      this.app = null;
    }
    
    if (this.mountElement && this.mountElement.parentNode) {
      this.mountElement.parentNode.removeChild(this.mountElement);
      this.mountElement = null;
    }
  }
  
  // 获取当前环境
  getEnvironment() {
    return environment;
  }
}

// 暴露 SDK 全局 API
window.AIChatSDK = {
  init: (config) => new AIChatSDK(config)
};

// 自动初始化（如果存在配置）
if (window.AIChatConfig) {
  window.aiChatInstance = window.AIChatSDK.init(window.AIChatConfig);
}

export default AIChatSDK; 