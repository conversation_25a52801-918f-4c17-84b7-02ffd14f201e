// 环境配置文件

// 获取当前环境
const getEnv = () => {
  // 优先从全局变量获取环境
  if (window.AIChatEnv) {
    return window.AIChatEnv;
  }
  
  // 根据域名推断环境（备选方案）
  const hostname = window.location.hostname;
  if (hostname.includes('dev.') || hostname === 'localhost' || hostname.startsWith('127.') || hostname.includes('.local')) {
    return 'dev';
  } else if (hostname.includes('test.') || hostname.includes('-test')) {
    return 'test';
  } else if (hostname.includes('canary.') || hostname.includes('-canary')) {
    return 'canary';
  } else {
    return 'prod';
  }
};

// 当前环境
const currentEnv = getEnv();

// 环境配置
const configs = {
	dev: {
		apiHost: "https://dev-gcluster.shukeyun.com",
		baseApiHost: "https://dev.shukeyun.com"
	},
	test: {
		apiHost: "https://test-gcluster.shukeyun.com",
		baseApiHost: "https://test.shukeyun.com"
	},
	canary: {
		apiHost: "https://canary-gcluster.shukeyun.com",
		baseApiHost: "https://canary.shukeyun.com"
	},
	prod: {
		apiHost: "https://gcluster.shukeyun.com",
		baseApiHost: "https://prod.shukeyun.com"
	}
}

// 导出当前环境配置
export default configs[currentEnv] || configs.prod;

// 导出环境名称，便于其他组件使用
export const environment = currentEnv;

// 判断当前是本地开发环境还是线上环境
export const isLocalDevelopment = () => {
  const hostname = window.location.hostname;
  return (
		hostname === "localhost" ||
		hostname.startsWith("127.") ||
		hostname.startsWith("192.168.") ||
		hostname.includes(".local") ||
		hostname === ""
	)
}; 