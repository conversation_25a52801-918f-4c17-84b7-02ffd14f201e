# AI SDK

基于 Vue 3 开发的 AI SDK，可以轻松集成到任何网站中，提供灵活的 iframe 内容展示。

## 功能特点

- 🚀 轻量级集成 - 简单引入 JS 文件即可使用
- 📱 响应式设计 - 在移动端和桌面端均可良好展示
- 🔧 高度可配置 - 自定义位置和行为
- 🔌 简洁 API - 提供便捷的开发接口
- 🌍 环境支持 - 轻松切换开发、测试、金丝雀和生产环境

## 快速开始

### 安装

```bash
# 克隆仓库
git clone https://git.shukeyun.com/research-and-development/common/ai-sdk.git
cd ai-sdk

# 安装依赖
npm install

# 开发模式启动
npm run dev

# 构建SDK
npm run build:sdk
```

构建的 SDK 文件将输出到 `dist` 目录中。

### 在网站中使用

1. 将构建后的 JS 文件添加到您的 HTML 中：

```html
<script src="path/to/ai-chat-sdk.js"></script>
```

2. 配置并初始化 SDK:

```html
<script>
  window.AIChatConfig = {
    position: 'bottom-right', // 位置设置
    offsetX: 20,              // 水平偏移量
    offsetY: 20,              // 垂直偏移量
    url: 'https://your-domain.com/your-page', // iframe 要加载的 URL
    onOpen: function() { console.log('窗口已打开'); },
    onClose: function() { console.log('窗口已关闭'); }
  };
</script>
```

3. 通过 JS 控制界面 (可选):

```javascript
// 打开窗口
window.aiChatInstance.openChat();

// 关闭窗口
window.aiChatInstance.closeChat();

// 销毁实例
window.aiChatInstance.destroy();
```

## 配置项说明

SDK 支持以下配置选项：

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| position | string | 'bottom-right' | 按钮的基础位置，可选值：'bottom-right'、'bottom-left'、'top-right'、'top-left'。决定按钮出现在屏幕的哪个角落。 |
| offsetX | number | 20 | 水平方向的偏移量（像素），根据 position 设置决定是距离左侧边缘还是右侧边缘的距离。 |
| offsetY | number | 20 | 垂直方向的偏移量（像素），根据 position 设置决定是距离顶部边缘还是底部边缘的距离。 |
| url | string | '' | iframe 要加载的 URL 地址。 |       
| onOpen | function | null | 窗口打开时的回调函数。 |
| onClose | function | null | 窗口关闭时的回调函数。 |

### position 与 offsetX/offsetY 配合使用

这些配置项组合起来可以精确控制按钮的位置：

- **position: 'bottom-right'** - 按钮定位在右下角
  - offsetX: 控制按钮距离右侧边缘的距离
  - offsetY: 控制按钮距离底部边缘的距离

- **position: 'bottom-left'** - 按钮定位在左下角
  - offsetX: 控制按钮距离左侧边缘的距离
  - offsetY: 控制按钮距离底部边缘的距离

- **position: 'top-right'** - 按钮定位在右上角
  - offsetX: 控制按钮距离右侧边缘的距离
  - offsetY: 控制按钮距离顶部边缘的距离

- **position: 'top-left'** - 按钮定位在左上角
  - offsetX: 控制按钮距离左侧边缘的距离
  - offsetY: 控制按钮距离顶部边缘的距离

### 环境配置

SDK 支持四种不同的环境:

- **dev** - 开发环境 (http://dev.abc.com)
- **test** - 测试环境 (http://test.abc.com)
- **canary** - 金丝雀环境 (http://canary.abc.com)
- **prod** - 生产环境 (http://abc.com)

设置环境的最简单方法是在加载 SDK 之前设置全局变量:

```html
<script>
  window.AIChatEnv = 'dev'; // 设置为开发环境
</script>
<script src="path/to/ai-chat-sdk.js"></script>
```

如果不设置环境变量，SDK 会尝试根据当前域名自动检测环境:
- 如果域名包含 localhost、127.* 或 *.local，则使用 dev 环境
- 如果域名包含 test. 或 -test，则使用 test 环境
- 如果域名包含 canary. 或 -canary，则使用 canary 环境
- 其他情况使用 prod 环境

## 演示

可以通过访问 `/sdk-demo.html` 查看 SDK 的演示页面。
您也可以通过访问 `/env-example.html` 查看环境配置示例。

## 开发指南

SDK 构建配置在 `vite.sdk.config.js` 文件中定义，它使用 Vite 的库模式构建一个可以集成到其他项目中的 SDK。

项目使用以下技术栈：
- Vue 3 组件化开发
- Vite 构建工具
- CSS 样式注入技术 (无需额外引入 CSS 文件)

## 许可证

MIT

## 贡献指南

1. Fork 这个仓库
2. 创建您的特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交您的更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启一个 Pull Request
