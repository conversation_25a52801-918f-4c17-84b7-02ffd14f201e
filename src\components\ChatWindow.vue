<template>
  <transition name="chat-window">
    <div class="ai-chat-window" v-show="isOpen">
      <div class="window-container">
        <!-- iframe 内容区域 -->
        <div class="iframe-container">
          <iframe v-if="url" :src="url" class="content-iframe" frameborder="0" allowfullscreen></iframe>
          <div v-else class="no-url-message">
            <p>未配置 iframe URL</p>
            <p>请在配置中设置 url 参数</p>
          </div>
        </div>
        <!-- 关闭按钮 -->
        <button class="close-btn" @click="$emit('close')">
          <span class="close-icon"></span>
        </button>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'ChatWindow',
  props: {
    isOpen: Boolean,
    config: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    url() {
      return this.config.url || '';
    }
  },
  watch: {
    isOpen(val) {
      console.log('窗口状态：', val ? '打开' : '关闭');
    }
  },
  mounted() {
    // 添加键盘事件监听
    document.addEventListener('keydown', this.handleKeyDown);
  },
  beforeUnmount() {
    // 移除键盘事件监听
    document.removeEventListener('keydown', this.handleKeyDown);
  },
  methods: {
    handleKeyDown(event) {
      // 如果按下 ESC 键且弹窗是打开的，则关闭弹窗
      if (event.key === 'Escape' && this.isOpen) {
        this.$emit('close');
      }
    }
  }
}
</script>

<style scoped lang="less">
.ai-chat-window {
  padding: 5px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 800px;
  height: 540px;
  border-radius: 20px;
  background-color: #73b2ff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.window-container {
  display: flex;
  height: 100%;
  border-radius: 20px;
  position: relative;
  background-color: #fff;
}

.iframe-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
  border-radius: 15px;
  overflow: hidden;
  margin: 5px;
}

.content-iframe {
  width: 100%;
  height: 100%;
  border: none;
    border-radius: 15px;
  }
  
  .no-url-message {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
      justify-content: center;
      color: #666;
        font-size: 16px;
      
        p {
          margin: 8px 0;
          text-align: center;
        }
      }


.close-btn {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 20px;
  color: white;
  cursor: pointer;
  z-index: 2;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f5f5f5;
  }
}

.close-icon {
  position: relative;
  width: 20px;
  height: 20px;

  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: #5a5a68;
    top: 50%;
    left: 0;
    border-radius: 3px;
  }

  &::before {
    transform: rotate(45deg);
  }

  &::after {
    transform: rotate(-45deg);
  }
}

/* 添加过渡动画 */
.chat-window-enter-active,
.chat-window-leave-active {
  transition: all 0.3s ease;
}

.chat-window-enter-from,
.chat-window-leave-to {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
}

.chat-window-enter-to,
.chat-window-leave-from {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}
</style> 