<template>
  <transition 
    name="message" 
    appear
    @enter="enter"
    @leave="leave"
  >
    <div class="message" :class="{ user: message.isUser }">
      <div class="message-content">
        <template v-if="message.isLoading">
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </template>
        <template v-else>
          <div v-if="message.isUser">{{ message.text }}</div>
          <div v-else v-html="renderMarkdown(message.text)"></div>
        </template>
      </div>
    </div>
  </transition>
</template>

<script>
import { marked } from 'marked';

// 创建自定义的 renderer
const renderer = new marked.Renderer();

// 配置 marked 选项
marked.setOptions({
  renderer: renderer,
  headerIds: false,
  mangle: false,
  gfm: true,
  breaks: true
});

export default {
  name: 'MessageItem',
  props: {
    message: {
      type: Object,
      required: true,
      validator: (value) => {
        return (typeof value.text === 'string' && 
               typeof value.isUser === 'boolean') ||
               (value.isLoading === true && 
               typeof value.isUser === 'boolean');
      }
    }
  },
  methods: {
    enter(el, done) {
      requestAnimationFrame(() => {
        el.style.transform = 'translateY(0)';
        el.style.opacity = '1';
        done();
      });
    },
    leave(el, done) {
      el.style.transform = 'translateY(20px)';
      el.style.opacity = '0';
      setTimeout(done, 300);
    },
    renderMarkdown(text) {
      if (!text) return '';
      try {
        // 使用 marked 的安全输出
        return marked.parse(text);
      } catch (error) {
        console.error('Markdown 渲染错误：', error);
        return text; // 如果渲染失败，返回原始文本
      }
    }
  }
}
</script>

<style scoped>
.message {
  margin-bottom: 10px;
  display: inline-flex;
  max-width: 80%;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
  width: fit-content;
}

.message.user {
  align-self: flex-end;
  margin-left: auto;
  color: #fff;
  padding: 12px 12px;
  background: linear-gradient(180deg, #35C3FF 0%, #1890FF 100%);
  border-radius: 10px 0px 10px 10px;
}

.message:not(.user) {
  align-self: flex-start;
  margin-right: auto;
  color: #000;
  background: rgba(255, 255, 255, 0.78);
  border-radius: 0px 10px 10px 10px;
  padding: 6px 12px;
}

.message-content {
  line-height: 1.5;
  font-size: 16px;
  /* white-space: pre-wrap; */
  word-break: break-word;
}

/* 添加 Markdown 样式 */
:deep(a) {
  color: #1890FF;
  text-decoration: none;
}

:deep(p) {
  margin: 6px 0;
}

:deep(pre) {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 6px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 6px 0;
}

:deep(code) {
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  font-size: 14px;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 4px;
  border-radius: 3px;
}

:deep(pre code) {
  background-color: transparent;
  padding: 0;
}

:deep(blockquote) {
  border-left: 4px solid #1890FF;
  margin: 6px 0;
  padding-left: 8px;
  color: #666;
}

:deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 6px 0;
}

:deep(th), :deep(td) {
  border: 1px solid #ddd;
  padding: 4px 6px;
  text-align: left;
}

:deep(th) {
  background-color: rgba(0, 0, 0, 0.05);
}

:deep(ul), :deep(ol) {
  display: block;
  list-style-position: outside;
  margin: 0;
  padding: 0;
  padding-left: 24px;
}

:deep(li) {
  margin: 2px 0;
  display: list-item;
}

:deep(li p) {
  display: inline-block;
  margin: 0;
}

:deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
  margin: 6px 0;
}

.loading-dots {
  display: flex;
  gap: 4px;
  padding: 4px;
  min-width: 40px;
  min-height: 24px;
  align-items: center;
  
  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #409eff;
    animation: loading 1.4s infinite ease-in-out both;
    
    &:nth-child(1) {
      animation-delay: -0.32s;
    }
    
    &:nth-child(2) {
      animation-delay: -0.16s;
    }
  }
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style> 