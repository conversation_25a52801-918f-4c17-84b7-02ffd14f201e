<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI 聊天 SDK 环境变量示例</title>
</head>
<body>
  <h1>AI 聊天 SDK 环境变量示例</h1>
  
  <div>
    <h2>环境选择</h2>
    <button onclick="setEnvironment('dev')">开发环境</button>
    <button onclick="setEnvironment('test')">测试环境</button>
    <button onclick="setEnvironment('canary')">金丝雀环境</button>
    <button onclick="setEnvironment('prod')">生产环境</button>
  </div>
  
  <div>
    <h2>控制聊天窗口</h2>
    <button onclick="openChat()">打开聊天</button>
    <button onclick="closeChat()">关闭聊天</button>
  </div>
  
  <script>
    // 设置环境并重新加载页面
    function setEnvironment(env) {
      window.AIChatEnv = env;
      // 这将导致页面重新加载，从而使用新的环境设置初始化SDK
      localStorage.setItem('AI_CHAT_ENV', env);
      
      // 销毁现有实例
      if (window.aiChatInstance) {
        window.aiChatInstance.destroy();
      }
      
      // 重新加载SDK脚本，使用新环境
      var oldScript = document.getElementById('ai-chat-sdk');
      if (oldScript) {
        oldScript.parentNode.removeChild(oldScript);
      }
      
      var script = document.createElement('script');
      script.id = 'ai-chat-sdk';
      script.src = `/js/ai-chat-sdk.js?env=${env}&t=${Date.now()}`;
      document.body.appendChild(script);
      
      // 初始化SDK
      script.onload = function() {
        window.AIChatConfig = {
          position: 'bottom-right',
          offsetX: 20,
          offsetY: 20,
          onOpen: function() {
            console.log('聊天窗口已打开');
          },
          onClose: function() {
            console.log('聊天窗口已关闭');
          }
        };
        
        window.aiChatInstance = window.AIChatSDK.init(window.AIChatConfig);
        
        // 显示当前环境
        document.getElementById('current-env').textContent = window.AIChatEnv;
      };
    }
    
    function openChat() {
      if (window.aiChatInstance) {
        window.aiChatInstance.openChat();
      } else {
        alert('SDK尚未初始化，请先选择环境');
      }
    }
    
    function closeChat() {
      if (window.aiChatInstance) {
        window.aiChatInstance.closeChat();
      }
    }
    
    // 页面加载完成后，恢复之前的环境设置（如果有）
    window.addEventListener('load', function() {
      var savedEnv = localStorage.getItem('AI_CHAT_ENV');
      if (savedEnv) {
        window.AIChatEnv = savedEnv;
        document.getElementById('current-env').textContent = savedEnv;
      }
    });
  </script>
  
  <div>
    <h3>当前环境: <span id="current-env">未设置</span></h3>
  </div>
  
  <!-- 加载SDK脚本 -->
  <script id="ai-chat-sdk" src="/js/ai-chat-sdk.js"></script>
</body>
</html> 