import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
	plugins: [vue(), vueDevTools()],
	resolve: {
		alias: {
			"@": fileURLToPath(new URL("./src", import.meta.url))
		}
	},
	server: {
		host: "0.0.0.0",
		port: 66, // 添加开发端口配置
		cors: true,
		proxy: {
			"/api": {
				target: "https://dev-gcluster.shukeyun.com",
				changeOrigin: true,
				rewrite: path => path.replace(/^\/api/, ""),
				configure: (proxy, _options) => {
					proxy.on("error", (err, _req, _res) => {
						console.log("proxy error", err)
					})
					proxy.on("proxyReq", (proxyReq, req, _res) => {
						console.log("Sending Request to the Target:", req.method, req.url)
					})
					proxy.on("proxyRes", (proxyRes, req, _res) => {
						console.log(
							"Received Response from the Target:",
							proxyRes.statusCode,
							req.url
						)
					})
				}
			},
			"/baseApi": {
				target: "https://dev.shukeyun.com",
				changeOrigin: true,
				rewrite: path => path.replace(/^\/baseApi/, ""),
				configure: (proxy, _options) => {
					proxy.on("error", (err, _req, _res) => {
						console.log("proxy error", err)
					})
					proxy.on("proxyReq", (proxyReq, req, _res) => {
						console.log("Sending Request to the Target:", req.method, req.url)
					})
					proxy.on("proxyRes", (proxyRes, req, _res) => {
						console.log(
							"Received Response from the Target:",
							proxyRes.statusCode,
							req.url
						)
					})
				}
			}
		}
	},
	build: {
		rollupOptions: {
			input: {
				main: fileURLToPath(new URL("./index.html", import.meta.url)),
				demo: fileURLToPath(new URL("./public/sdk-demo.html", import.meta.url)),
				simple: fileURLToPath(
					new URL("./public/simple-example.html", import.meta.url)
				)
			}
		}
	}
})
