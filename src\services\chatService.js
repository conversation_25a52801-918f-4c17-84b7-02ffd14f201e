// 聊天服务
import { fetchEventSource } from '@microsoft/fetch-event-source';
import config, { isLocalDevelopment } from '../config/environment';

export class ChatService {
  constructor() {
    // 不再需要 apiKey
  }
  
  /**
   * 使用服务器发送事件 (SSE) 发送消息并获取流式响应
   * @param {Object} params 请求参数
   * @param {string} params.question 要发送的问题
   * @param {string} params.chatid 会话 ID
   * @param {string[]} params.docids 文档 ID 数组
   * @param {Object} callbacks 回调函数集合
   * @param {Function} callbacks.onMessage 收到消息时的回调
   * @param {Function} callbacks.onError 发生错误时的回调
   * @param {Function} callbacks.onClose 连接关闭时的回调
   * @param {AbortController} abortController 用于中止请求的控制器
   * @returns {Promise<void>}
   */
  async sendMessageStream(params, callbacks = {}, abortController = new AbortController()) {
    const { onMessage, onError, onClose } = callbacks;

    let apiHost = config.apiHost;

    // if(isLocalDevelopment()){
    //   apiHost = '/api';
    // }

    try {
      await fetchEventSource(
				`${apiHost}/algorithm/intelligent-customer-service/chat/completion`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						// 移除 apiKey 相关授权头
					},
					body: JSON.stringify({
						chat_id: params.chatid,
						doc_ids: params.docids,
						question: params.question
					}),
					signal: abortController.signal,

					async onopen(response) {
						if (!response.ok) {
							throw new Error(`HTTP error! status: ${response.status}`)
						}
					},

					onmessage(event) {
						if (onMessage && typeof onMessage === "function") {
							try {
								const data = event.data ? JSON.parse(event.data) : '\n'
								onMessage(data)
							} catch (e) {
								onMessage(event.data)
							}
						}
					},

					onerror(err) {
						if (onError && typeof onError === "function") {
							onError(err)
						}
						throw err // 重新抛出错误以便结束连接
					},

					onclose() {
						if (onClose && typeof onClose === "function") {
							onClose()
						}
					}
				}
			)
    } catch (error) {
      console.error('流式消息处理失败：', error);
      if (onError && typeof onError === 'function') {
        onError(error);
      }
      throw error;
    }
  }
} 